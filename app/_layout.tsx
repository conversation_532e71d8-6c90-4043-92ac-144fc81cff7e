import '../global.css';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { useFonts } from 'expo-font';
import { Redirect, Stack, useSegments } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { useEffect, useState } from 'react';
import { useAuthStore } from '@/stores/auth-store';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { useSyncQueueStore } from '@/stores/sync-queue-store';
import { logger } from '@/utils/logger';
import { commonStyles } from '@/styles/commonStyles';
import { FeedbackManager } from '@/app/components/FeedbackManager';
import ChatFloatingButton from '@/components/navigation/ChatFloatingButton';

export const unstable_settings = {
  initialRouteName: '(tabs)',
};

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [loaded, error] = useFonts({
    ...FontAwesome.font,
  });

  useEffect(() => {
    if (error) {
      logger.error('Font loading error', 'RootLayout', error);
      // Don't throw error, just log it and continue
    }
  }, [error]);

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return <RootLayoutNav />;
}

function RootLayoutNav() {
  const segments = useSegments();
  const { isAuthenticated, initializeAuth, user } = useAuthStore();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Initialize authentication from Supabase
    const init = async () => {
      try {
        await initializeAuth();
      } catch (error) {
        logger.error('Error initializing auth', 'RootLayout', error);
      } finally {
        setIsLoading(false);
      }
    };

    init();
  }, [initializeAuth]);

  // Initialize network listener for sync queue
  useEffect(() => {
    const { initializeNetworkListener } = useSyncQueueStore.getState();
    const unsubscribe = initializeNetworkListener();

    // Only process sync queue if authenticated and user has a salon
    if (isAuthenticated && user && user.salonId) {
      const { processSyncQueue, getQueueStatus } = useSyncQueueStore.getState();
      const status = getQueueStatus();
      if (status.pending > 0 || status.failed > 0) {
        processSyncQueue();
      }
    }

    return () => {
      unsubscribe();
    };
  }, [isAuthenticated, user]);

  // Show loading state
  if (isLoading) {
    return null;
  }

  // Check if user is in special routes
  const inAuthGroup = segments[0] === 'auth';
  const inOnboardingGroup = segments[0] === 'onboarding';

  // If the user is not authenticated and we're not in auth or onboarding, redirect to auth
  if (!isAuthenticated && !inAuthGroup && !inOnboardingGroup) {
    return <Redirect href="/auth/login" />;
  }

  // If the user is authenticated and we're in the auth group, redirect to home
  if (isAuthenticated && inAuthGroup) {
    return <Redirect href="/(tabs)" />;
  }

  return (
    <GestureHandlerRootView style={commonStyles.flex1}>
      <FeedbackManager>
        <Stack
          screenOptions={{
            headerBackTitle: 'Atrás',
          }}
        >
          <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
          <Stack.Screen name="auth" options={{ headerShown: false }} />
          <Stack.Screen name="onboarding" options={{ headerShown: false }} />
          <Stack.Screen
            name="client/[id]"
            options={{ headerShown: false, title: 'Detalle del Cliente' }}
          />
          <Stack.Screen
            name="client/new"
            options={{ headerShown: false, title: 'Nuevo Cliente' }}
          />
          <Stack.Screen
            name="service/client-selection"
            options={{ headerShown: false, title: 'Seleccionar Cliente' }}
          />
          <Stack.Screen
            name="service/safety-verification"
            options={{ headerShown: false, title: 'Verificación de Seguridad' }}
          />
          <Stack.Screen
            name="service/new"
            options={{ headerShown: false, title: 'Nuevo Servicio' }}
          />
          <Stack.Screen
            name="inventory/new"
            options={{ headerShown: false, title: 'Añadir Producto' }}
          />
          <Stack.Screen
            name="inventory/[id]"
            options={{ headerShown: false, title: 'Detalle del Producto' }}
          />
          <Stack.Screen name="modal" options={{ presentation: 'modal' }} />
        </Stack>

        {/* Chat floating button for chat-first navigation */}
        <ChatFloatingButton />
      </FeedbackManager>
    </GestureHandlerRootView>
  );
}
