import { useEffect, useState } from 'react';
import { router, usePathname } from 'expo-router';
import { useChatStore } from '@/stores/chat-store';

interface ChatNavigationOptions {
  contextType?: 'client' | 'service' | 'inventory' | 'general';
  contextId?: string;
  contextData?: Record<string, any>;
  autoCreateConversation?: boolean;
}

export function useChatNavigation() {
  const pathname = usePathname();
  const { createNewConversation, setActiveConversation } = useChatStore();
  const [isOnChatScreen, setIsOnChatScreen] = useState(false);

  useEffect(() => {
    setIsOnChatScreen(pathname === '/(tabs)/assistant');
  }, [pathname]);

  const navigateToChat = async (options: ChatNavigationOptions = {}) => {
    const {
      contextType = 'general',
      contextId,
      contextData,
      autoCreateConversation = true
    } = options;

    try {
      // Create new conversation with context if needed
      if (autoCreateConversation && (contextType !== 'general' || contextId)) {
        const conversation = await createNewConversation({
          contextType,
          contextId,
          metadata: {
            sourceScreen: pathname,
            contextData,
            createdAt: new Date().toISOString(),
          }
        });

        if (conversation) {
          setActiveConversation(conversation.id);
        }
      }

      // Navigate to chat
      router.push('/(tabs)/assistant');

      return true;
    } catch (error) {
      console.error('Error navigating to chat:', error);
      return false;
    }
  };

  const navigateToChatWithClient = (clientId: string, clientData?: any) => {
    return navigateToChat({
      contextType: 'client',
      contextId: clientId,
      contextData: clientData,
    });
  };

  const navigateToChatWithService = (serviceId: string, serviceData?: any) => {
    return navigateToChat({
      contextType: 'service',
      contextId: serviceId,
      contextData: serviceData,
    });
  };

  const navigateToChatWithInventory = (productId: string, productData?: any) => {
    return navigateToChat({
      contextType: 'inventory',
      contextId: productId,
      contextData: productData,
    });
  };

  const askChatAbout = async (question: string, options: ChatNavigationOptions = {}) => {
    const success = await navigateToChat(options);
    
    if (success) {
      // TODO: Auto-send the question to chat
      // This would require access to the chat store's sendMessage function
      // For now, we just navigate and let the user type the question
    }
    
    return success;
  };

  return {
    isOnChatScreen,
    navigateToChat,
    navigateToChatWithClient,
    navigateToChatWithService,
    navigateToChatWithInventory,
    askChatAbout,
  };
}
