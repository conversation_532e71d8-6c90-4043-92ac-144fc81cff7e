import React, { useState } from 'react';
import { 
  View, 
  TouchableOpacity, 
  StyleSheet, 
  Animated,
  Dimensions 
} from 'react-native';
import { router, usePathname } from 'expo-router';
import { MessageCircle, X } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { spacing, radius, shadows } from '@/constants/theme';

interface ChatFloatingButtonProps {
  style?: any;
}

const { width: screenWidth } = Dimensions.get('window');

export default function ChatFloatingButton({ style }: ChatFloatingButtonProps) {
  const pathname = usePathname();
  const [isExpanded, setIsExpanded] = useState(false);
  const scaleAnim = new Animated.Value(1);

  // Don't show on chat/assistant screen
  if (pathname === '/(tabs)/assistant') {
    return null;
  }

  const handlePress = () => {
    // Animate button press
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.9,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    // Navigate to chat
    router.push('/(tabs)/assistant');
  };

  const handleLongPress = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <View style={[styles.container, style]}>
      <Animated.View 
        style={[
          styles.button,
          {
            transform: [{ scale: scaleAnim }],
          }
        ]}
      >
        <TouchableOpacity
          style={styles.touchable}
          onPress={handlePress}
          onLongPress={handleLongPress}
          activeOpacity={0.8}
        >
          <MessageCircle 
            size={24} 
            color="white" 
            strokeWidth={2}
          />
        </TouchableOpacity>
      </Animated.View>

      {/* Pulse animation for attention */}
      <View style={styles.pulseContainer}>
        <Animated.View style={styles.pulse} />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 100, // Above tab bar
    right: spacing.lg,
    zIndex: 1000,
  },
  button: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.light.primary,
    justifyContent: 'center',
    alignItems: 'center',
    ...shadows.lg,
    elevation: 8,
  },
  touchable: {
    width: '100%',
    height: '100%',
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
  },
  pulseContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: -1,
  },
  pulse: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.light.primary,
    opacity: 0.3,
  },
});
