import React, { useState, useRef, useEffect, useCallback, memo } from 'react';
import { useTimer, useMemoryMonitor, useSafeAsync } from '@/utils/memory-cleanup';
import { useMemoWithTTL, useStableCallback } from '@/utils/memoization-utils';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Alert,
  Image,
  Dimensions,
  Keyboard,
} from 'react-native';
import { Send, Camera, Image as ImageIcon, Sparkles, Menu, X, Home } from 'lucide-react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import * as ImagePicker from 'expo-image-picker';
import { usePhotoAnalysis } from '@/src/service/hooks/usePhotoAnalysis';
import Colors from '@/constants/colors';
import { spacing, typography, radius } from '@/constants/theme';
import { useChatStore, ChatMessage, ChatAttachment } from '@/stores/chat-store';
import { useAuthStore } from '@/stores/auth-store';
import { useClientStore } from '@/stores/client-store';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { logger } from '@/utils/logger';
import { ImageProcessor } from '@/utils/image-processor';
import { commonStyles } from '@/styles/commonStyles';
import SmartSuggestions from './SmartSuggestions';
import TypingIndicator from './TypingIndicator';
import ConversationsList from './ConversationsList';
import StreamingMessage from './StreamingMessage';
import QuickActions from './QuickActions';
import SmartInsights from './SmartInsights';
import QuickMetrics from './QuickMetrics';
import ContextualSuggestions from './ContextualSuggestions';
import ChatTransition from './ChatTransition';
// Modals removed - using ActionSheet instead
// import ImagePickerModal from './ImagePickerModal';
// import ImageContextModal, { ImageContextType } from './ImageContextModal';

// Type from ImageContextModal - keeping for compatibility
type ImageContextType = 'current' | 'desired' | 'result' | 'general';

interface ContextData {
  name?: string;
  [key: string]: unknown;
}

interface ChatGPTInterfaceProps {
  conversationId?: string;
  contextType?: 'general' | 'client' | 'service' | 'formula' | 'inventory';
  contextId?: string;
  contextData?: ContextData;
  onClose?: () => void;
  isModal?: boolean;
}

function ChatGPTInterface({
  conversationId: propConversationId,
  contextType,
  contextId,
  contextData,
  onClose: _onClose,
  isModal = false,
}: ChatGPTInterfaceProps) {
  // Memory monitoring for performance optimization
  useMemoryMonitor('ChatGPTInterface');

  // Safe async operations
  const { execute: safeExecute } = useSafeAsync();

  // Timer management with automatic cleanup
  const { setTimeout: safeSetTimeout, clearAll: clearAllTimers } = useTimer();
  const insets = useSafeAreaInsets();
  const scrollViewRef = useRef<ScrollView>(null);
  const [message, setMessage] = useState('');
  const [_isTyping, setIsTyping] = useState(false);
  const [showSidebar, setShowSidebar] = useState(false);
  const [pendingAttachments, setPendingAttachments] = useState<ChatAttachment[]>([]);
  const [_keyboardHeight, setKeyboardHeight] = useState(0);
  const { user: _user } = useAuthStore();

  // Check if device is tablet
  const { width } = Dimensions.get('window');
  const IS_TABLET = width >= 768;

  // Chat store
  const {
    conversations,
    messages,
    activeConversationId,
    isSending,
    isLoading,
    error: _error,
    streamingMessage,
    typingStatus,
    sendMessage,
    loadMessages,
    loadConversations,
    setActiveConversation,
    createConversation,
    archiveConversation,
    deleteConversation,
    toggleFavorite,
    cleanupStreaming,
    cleanupConversationMemory,
  } = useChatStore();

  // Context stores
  const { clients: _clients } = useClientStore();
  const { configuration: _configuration } = useSalonConfigStore();

  // Use the working photo analysis hook
  const { takePhoto: _takePhoto, pickImage: _pickImage } = usePhotoAnalysis();

  const currentMessages = activeConversationId ? messages[activeConversationId] || [] : [];

  // Initialize sidebar for tablets
  useEffect(() => {
    if (IS_TABLET) {
      setShowSidebar(true);
    }
  }, [IS_TABLET]);

  // Enhanced keyboard handling with auto-scroll
  useEffect(() => {
    const keyboardWillShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      event => {
        setKeyboardHeight(event.endCoordinates.height);

        // Auto-scroll to bottom when keyboard appears to ensure input is visible
        safeSetTimeout(
          () => {
            scrollViewRef.current?.scrollToEnd({ animated: true });
          },
          Platform.OS === 'ios' ? 100 : 300
        );
      }
    );

    const keyboardWillHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        setKeyboardHeight(0);
      }
    );

    return () => {
      keyboardWillShowListener.remove();
      keyboardWillHideListener.remove();
    };
  }, [safeSetTimeout]);

  // Auto-scroll when streaming
  useEffect(() => {
    if (streamingMessage && streamingMessage.conversationId === activeConversationId) {
      // Smooth scroll to bottom while streaming
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }
  }, [streamingMessage?.content, streamingMessage, activeConversationId]);

  // Initialize conversation
  useEffect(() => {
    const initializeChat = async () => {
      // Load conversations first
      await loadConversations();

      // CRITICAL FIX: Use get() to get the updated state after loadConversations
      // This prevents race condition where conversations is empty
      const chatStore = useChatStore.getState();
      const updatedConversations = chatStore.conversations;

      if (propConversationId) {
        setActiveConversation(propConversationId);
        await loadMessages(propConversationId);
      } else if (contextType && contextId) {
        // Check if conversation exists for this context using updated conversations
        const existingConv = updatedConversations.find(
          conv => conv.contextType === contextType && conv.contextId === contextId
        );

        if (existingConv) {
          setActiveConversation(existingConv.id);
          await loadMessages(existingConv.id);
        } else {
          // Create new conversation with context
          const title = generateWelcomeTitle();
          const newConv = await createConversation({
            title,
            contextType,
            contextId,
            metadata: { contextData: contextData || {} },
          });
          if (newConv) {
            setActiveConversation(newConv.id);
          }
        }
      } else if (!activeConversationId && updatedConversations.length > 0) {
        // Select the most recent conversation using updated conversations
        const mostRecent = updatedConversations[0];
        setActiveConversation(mostRecent.id);
        await loadMessages(mostRecent.id);
      }
    };

    initializeChat();
  }, [
    propConversationId,
    contextType,
    contextId,
    contextData,
    loadConversations,
    setActiveConversation,
    loadMessages,
    createConversation,
    activeConversationId,
    generateWelcomeTitle,
  ]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearAllTimers();
      cleanupStreaming();
      cleanupConversationMemory();
    };
  }, [clearAllTimers, cleanupStreaming, cleanupConversationMemory]);

  // Generate contextual welcome title
  const generateWelcomeTitle = useCallback(() => {
    if (contextType === 'client' && contextData?.name) {
      return `Consulta sobre ${contextData.name}`;
    }
    if (contextType === 'service') {
      return 'Consulta sobre servicio';
    }
    if (contextType === 'formula') {
      return 'Consulta sobre fórmula';
    }
    if (contextType === 'inventory') {
      return 'Consulta de inventario';
    }
    return 'Nueva conversación';
  }, [contextType, contextData?.name]);

  // Generate intelligent conversation starters - memoized with TTL for performance
  const getConversationStarters = useMemoWithTTL(
    () => {
      const starters = [];

      // Context-based starters
      if (contextType === 'client' && contextData) {
        starters.push(`¿Qué técnica recomiendas para ${contextData.name}?`);
      } else {
        // Only 2 most relevant general starters
        starters.push('¿Cómo corrijo un color naranja?');
        starters.push('Fórmula para rubio ceniza nivel 8');
      }

      return starters.slice(0, 2); // Maximum 2 starters
    },
    [contextType, contextData?.name],
    300000
  ); // 5 minutes TTL

  // Handle message send - ChatGPT/Claude style with pending attachments
  const handleSend = async () => {
    if (!message.trim() || isSending) {
      logger.debug('handleSend blocked:', { message: message.trim(), isSending });
      return;
    }

    const messageToSend = message.trim();
    const attachmentsToSend = [...pendingAttachments]; // Copy current attachments

    logger.debug('handleSend called:', { messageToSend, attachmentsCount: attachmentsToSend.length });

    // Clear input and pending attachments
    setMessage('');
    setPendingAttachments([]);

    try {
      // Send message with any pending attachments
      await safeExecute(() =>
        sendMessage(
          messageToSend,
          activeConversationId || undefined,
          attachmentsToSend.length > 0 ? attachmentsToSend : undefined
        )
      );

      logger.debug('Message sent successfully');

      // Scroll to bottom after sending - using safe timer
      safeSetTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    } catch (error) {
      logger.error('Error sending message', 'ChatGPTInterface', error);
      // Restore attachments if send failed
      setPendingAttachments(attachmentsToSend);
      Alert.alert('Error', 'No se pudo enviar el mensaje. Inténtalo de nuevo.');
    }
  };

  // Handle sending a specific message (for suggestions)
  const handleSendMessage = async (messageText: string) => {
    if (!messageText.trim() || isSending) {
      logger.debug('handleSendMessage blocked:', { messageText: messageText.trim(), isSending });
      return;
    }

    logger.debug('handleSendMessage called:', {
      messageText: messageText.trim(),
      activeConversationId,
      hasActiveConversation: !!activeConversationId
    });

    try {
      // Send message - let the store handle conversation creation if needed
      await safeExecute(() =>
        sendMessage(
          messageText.trim(),
          activeConversationId || undefined,
          undefined
        )
      );

      logger.debug('Suggestion message sent successfully');

      // Scroll to bottom after sending
      safeSetTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    } catch (error) {
      logger.error('Error sending suggestion message', 'ChatGPTInterface', error);
      Alert.alert('Error', 'No se pudo enviar el mensaje. Inténtalo de nuevo.');
    }
  };

  // Handle typing
  const handleTextChange = (text: string) => {
    setMessage(text);
    setIsTyping(text.length > 0);
  };

  // Handle suggestion selection - memoized for performance
  const handleSuggestionSelect = useStableCallback((suggestion: string) => {
    setMessage(suggestion);
  }, []);

  // Handle conversation selection - with memory cleanup
  const handleSelectConversation = useStableCallback(
    async (id: string) => {
      // Cleanup old conversation memory when switching
      cleanupConversationMemory(id);

      setActiveConversation(id);
      await safeExecute(() => loadMessages(id));
      if (!IS_TABLET) {
        setShowSidebar(false);
      }
    },
    [IS_TABLET, cleanupConversationMemory, setActiveConversation, loadMessages]
  );

  // Handle new conversation
  const handleCreateNewConversation = async () => {
    const title = 'Nueva conversación';
    const newConv = await createConversation({ title });
    if (newConv) {
      setActiveConversation(newConv.id);
      if (!IS_TABLET) {
        setShowSidebar(false);
      }
    }
  };

  // Handle go to home (welcome screen)
  const handleGoHome = () => {
    setActiveConversation(null);
    setShowSidebar(false);
  };

  // Handle swipe gesture to go home
  const handleSwipeGesture = (event: any) => {
    const { nativeEvent } = event;
    if (activeConversationId && currentMessages.length > 0) {
      // Detect swipe right gesture
      if (nativeEvent.velocityX > 500 && nativeEvent.translationX > 100) {
        handleGoHome();
      }
    }
  };

  // Toggle sidebar
  const toggleSidebar = () => {
    setShowSidebar(!showSidebar);
  };

  // Direct image upload - ChatGPT/Claude style (no alerts)
  const handleImageUpload = useCallback(
    async (source: 'camera' | 'library') => {
      // Upload image without predefined context - let user specify what they need
      await handleImageSelection(source, 'general', ''); // Empty context, user will provide
    },
    [handleImageSelection]
  );

  // Quick Actions handlers - Simplified
  const _handleQuickAction = async (action: string) => {
    switch (action) {
      case 'new-service':
        await handleSendMessage('Quiero iniciar un nuevo servicio de color');
        break;
      case 'photo-analysis':
        showImageSelectionOptions();
        break;
      case 'quick-formula':
        // Clear message so user can type their question
        setMessage('');
        // Focus on input
        break;
      default:
        break;
    }
  };

  // Simplified image selection with context
  const handleImageSelection = useCallback(
    async (source: 'camera' | 'library', context: ImageContextType, contextMessage: string) => {
      logger.debug('handleImageSelection', 'ChatGPTInterface', { source, context });

      try {
        if (source === 'camera') {
          logger.debug('Direct camera implementation', 'ChatGPTInterface');

          // Request permissions directly
          const { status } = await ImagePicker.requestCameraPermissionsAsync();
          if (status !== 'granted') {
            Alert.alert('Permisos necesarios', 'Se necesitan permisos de cámara para tomar fotos');
            return;
          }

          logger.debug('Launching camera directly', 'ChatGPTInterface');

          // Use the most minimal configuration possible
          const result = await ImagePicker.launchCameraAsync({
            mediaTypes: ['images'],
            quality: 0.5,
            base64: false,
          });

          logger.debug('Direct camera result', 'ChatGPTInterface', result);

          if (!result.canceled && result.assets && result.assets[0]) {
            const asset = result.assets[0];
            // Process image directly with context message
            await processImageWithContext(asset, contextMessage);
          }
        } else {
          logger.debug('Direct gallery implementation', 'ChatGPTInterface');

          // Request permissions directly
          const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
          if (status !== 'granted') {
            Alert.alert(
              'Permisos necesarios',
              'Se necesitan permisos de galería para seleccionar fotos'
            );
            return;
          }

          logger.debug('Launching gallery directly', 'ChatGPTInterface');

          // Allow multiple images for better analysis (max 3)
          const result = await ImagePicker.launchImageLibraryAsync({
            mediaTypes: ['images'],
            quality: 0.5,
            base64: false,
            allowsMultipleSelection: true,
            selectionLimit: 3,
          });

          logger.debug('Direct gallery result', 'ChatGPTInterface', result);

          if (!result.canceled && result.assets && result.assets.length > 0) {
            // Process all selected images
            await processMultipleImagesWithContext(result.assets, contextMessage);
          }
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger.error('Error in direct image picker', 'ChatGPTInterface', error);
        if (__DEV__ && error instanceof Error) {
          logger.error('Error details', 'ChatGPTInterface', {
            message: error.message,
            stack: error.stack,
            name: error.name,
          });
        }
        Alert.alert('Error', `Error al seleccionar imagen: ${errorMessage}`);
      }
    },
    [processImageWithContext]
  );

  // Process multiple images and attach to current message - ChatGPT/Claude style
  const processMultipleImagesWithContext = useCallback(
    async (assets: { uri: string; fileSize?: number }[], contextMessage: string) => {
      try {
        const attachments: ChatAttachment[] = [];

        // Process each image
        for (const asset of assets) {
          // Show size info only in dev
          const originalSizeMB = asset.fileSize
            ? (asset.fileSize / (1024 * 1024)).toFixed(1)
            : 'Desconocido';
          if (__DEV__) logger.info(`Processing image: ${originalSizeMB}MB original size`);

          // Compress image
          const compressedBase64 = await ImageProcessor.compressForUpload(asset.uri, true, 'chat');
          const imageDataUrl = `data:image/jpeg;base64,${compressedBase64}`;

          // Calculate compressed size
          const compressedSizeKB = Math.round(imageDataUrl.length / 1024);
          if (__DEV__) logger.info(`Compressed to: ${compressedSizeKB}KB`);

          // Add to attachments
          attachments.push({
            type: 'image' as const,
            url: imageDataUrl,
            mimeType: 'image/jpeg',
          });
        }

        // Instead of sending immediately, store the attachments and let user type their message
        // This mimics ChatGPT/Claude behavior where images are attached but user types what they want
        if (contextMessage) {
          // If there's a predefined context (legacy), send immediately
          await sendMessage(contextMessage, activeConversationId || undefined, attachments);
        } else {
          // ChatGPT/Claude style: Just show the images are attached and wait for user message
          // Limit total attachments to 3 to prevent overwhelming the interface
          setPendingAttachments(prev => {
            const combined = [...prev, ...attachments];
            if (combined.length > 3) {
              Alert.alert(
                'Límite de imágenes',
                'Máximo 3 imágenes por consulta. Se han seleccionado las primeras 3.',
                [{ text: 'OK' }]
              );
              return combined.slice(0, 3);
            }
            return combined;
          });
          // No alert needed - the visual preview is enough feedback
        }

        // Scroll to bottom after processing - using safe timer
        safeSetTimeout(() => {
          scrollViewRef.current?.scrollToEnd({ animated: true });
        }, 100);
      } catch (error) {
        logger.error('Error processing images', 'ChatGPTInterface', error);
        Alert.alert('Error', 'No se pudo procesar las imágenes. Inténtalo de nuevo.');
      }
    },
    [activeConversationId, sendMessage, safeSetTimeout]
  );

  // Process single image and attach to current message - ChatGPT/Claude style
  const processImageWithContext = useCallback(
    async (asset: { uri: string; fileSize?: number }, contextMessage: string) => {
      await processMultipleImagesWithContext([asset], contextMessage);
    },
    [processMultipleImagesWithContext]
  );

  // Deprecated functions - removed in favor of ActionSheet
  // const handleImageUpload = async (asset: any) => { ... }
  // const handleImageContextSelect = async (context, message) => { ... }

  // Render welcome screen - Chat-first with integrated widgets
  const renderWelcomeScreen = () => {
    const starters = getConversationStarters;

    return (
      <View style={styles.welcomeContainer}>
        <View style={styles.welcomeContent}>
          <View style={styles.welcomeLogo}>
            <Sparkles size={32} color={Colors.light.primary} />
          </View>
          <Text style={styles.welcomeTitle}>Salonier Assistant</Text>
          <Text style={styles.welcomeSubtitle}>Tu asistente inteligente de coloración</Text>

          {/* Quick Metrics Widget */}
          <QuickMetrics style={styles.widgetContainer} />

          {/* Quick Actions Widget */}
          <QuickActions
            onActionPress={(action) => {
              logger.debug('Quick action pressed:', action);
            }}
            style={styles.widgetContainer}
          />

          {/* Smart Insights Widget */}
          <SmartInsights style={styles.widgetContainer} />

          {/* Contextual Suggestions */}
          <ContextualSuggestions
            onSuggestionPress={(suggestion) => {
              handleSendMessage(suggestion);
            }}
            style={styles.widgetContainer}
          />

          {/* Conversation starters */}
          {starters.length > 0 && (
            <View style={styles.suggestionChips}>
              <Text style={styles.startersTitle}>O pregúntame algo:</Text>
              {starters.slice(0, 3).map((starter, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.suggestionChip}
                  onPress={() => {
                    handleSendMessage(starter);
                  }}
                  activeOpacity={0.6}
                >
                  <Text style={styles.suggestionText}>{starter}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>
      </View>
    );
  };

  // Render message bubble
  const renderMessage = (msg: ChatMessage, index: number) => {
    const isUser = msg.role === 'user';

    return (
      <View
        key={msg.id}
        style={[
          styles.messageContainer,
          isUser ? styles.userMessageContainer : styles.assistantMessageContainer,
        ]}
      >
        {!isUser && (
          <View style={styles.assistantAvatar}>
            <Sparkles size={14} color={Colors.light.primary} />
          </View>
        )}

        <View style={[styles.messageBubble, isUser ? styles.userBubble : styles.assistantBubble]}>
          {/* Render attachments if any */}
          {msg.attachments && msg.attachments.length > 0 && (
            <View style={styles.attachmentsContainer}>
              {msg.attachments.map((attachment, attachIndex) => (
                <View key={attachIndex} style={styles.attachmentContainer}>
                  {attachment.type === 'image' && (
                    <Image
                      source={{ uri: attachment.url }}
                      style={styles.attachmentImage}
                      resizeMode="cover"
                    />
                  )}
                </View>
              ))}
            </View>
          )}

          {/* Render message text with StreamingMessage */}
          {msg.content && (
            <StreamingMessage
              content={msg.content}
              isUser={isUser}
              isStreaming={!isUser && index === currentMessages.length - 1 && isSending}
              maxPreviewLength={300}
            />
          )}
        </View>
      </View>
    );
  };

  // Calculate proper keyboard offset with enhanced logic
  const getKeyboardVerticalOffset = () => {
    if (isModal) {
      return 0;
    }

    // For iOS, we need to account for:
    // - Status bar height (insets.top)
    // - Navigation header height (approximately 44-50 points)
    // - Safe area bottom (insets.bottom)
    if (Platform.OS === 'ios') {
      // More precise calculation for iOS
      return insets.top + 44 - insets.bottom;
    }

    // For Android, usually no offset needed with 'height' behavior
    return 0;
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={getKeyboardVerticalOffset()}
      enabled={true}
    >
      {/* Sidebar for tablets or when shown */}
      {(IS_TABLET || showSidebar) && (
        <>
          {!IS_TABLET && showSidebar && (
            <TouchableOpacity
              style={styles.sidebarBackdrop}
              activeOpacity={1}
              onPress={() => setShowSidebar(false)}
            />
          )}
          <View style={[styles.sidebar, !IS_TABLET && styles.sidebarOverlay]}>
            <ConversationsList
              conversations={conversations}
              activeConversationId={activeConversationId}
              isLoading={isLoading}
              onSelectConversation={handleSelectConversation}
              onArchiveConversation={archiveConversation}
              onDeleteConversation={deleteConversation}
              onNewConversation={handleCreateNewConversation}
              onToggleFavorite={toggleFavorite}
            />
          </View>
        </>
      )}

      {/* Main chat area */}
      <View
        style={[styles.mainContent, (IS_TABLET || showSidebar) && styles.mainContentWithSidebar]}
      >
        {/* Header - Chat-first with Home button */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            {!IS_TABLET && (
              <TouchableOpacity onPress={toggleSidebar} style={styles.menuButton}>
                <Menu size={20} color={Colors.light.textSecondary} />
              </TouchableOpacity>
            )}
            <View style={styles.titleContainer}>
              <Text style={styles.headerTitle}>Salonier</Text>
              {activeConversationId && currentMessages.length > 0 && (
                <View style={styles.conversationIndicator} />
              )}
            </View>

            {/* Show Home button when in conversation */}
            {activeConversationId && currentMessages.length > 0 ? (
              <TouchableOpacity onPress={handleGoHome} style={styles.homeButton}>
                <Home size={20} color={Colors.light.primary} />
              </TouchableOpacity>
            ) : (
              <View style={commonStyles.width20} />
            )}
          </View>
        </View>

        {/* Messages */}
        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesContainer}
          contentContainerStyle={styles.messagesContent}
          showsVerticalScrollIndicator={false}
        >
          <ChatTransition isVisible={currentMessages.length === 0} direction="fade">
            {currentMessages.length === 0 && renderWelcomeScreen()}
          </ChatTransition>

          <ChatTransition isVisible={currentMessages.length > 0} direction="slide">
            {currentMessages.length > 0 && (
              <>
                {currentMessages.map((msg, index) => renderMessage(msg, index))}
                {/* Render streaming message if active */}
                {streamingMessage && streamingMessage.conversationId === activeConversationId && (
                  <View style={[styles.messageContainer, styles.assistantMessageContainer]}>
                    <View style={styles.assistantAvatar}>
                      <Sparkles size={14} color={Colors.light.primary} />
                    </View>
                    <View style={[styles.messageBubble, styles.assistantBubble]}>
                      <StreamingMessage
                        content={streamingMessage.content}
                        isUser={false}
                        isStreaming={true}
                        maxPreviewLength={10000} // Don't truncate streaming messages
                      />
                    </View>
                  </View>
                )}
                <TypingIndicator visible={isSending && !streamingMessage} status={typingStatus} />
              </>
            )}
          </ChatTransition>
        </ScrollView>

        {/* Input Container with Smart Suggestions */}
        <View style={styles.bottomContainer}>
          {/* Smart Suggestions - Above input for better visibility */}
          {message.length > 0 && (
            <SmartSuggestions
              input={message}
              onSuggestionSelect={handleSuggestionSelect}
              contextType={contextType}
              contextData={contextData}
            />
          )}

          {/* Pending attachments preview - ChatGPT/Claude style */}
          {pendingAttachments.length > 0 && (
            <View style={styles.pendingAttachmentsContainer}>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.pendingAttachmentsScroll}
                contentContainerStyle={styles.pendingAttachmentsContent}
              >
                {pendingAttachments.map((attachment, index) => (
                  <View key={index} style={styles.pendingImageWrapper}>
                    <Image
                      source={{ uri: attachment.url }}
                      style={styles.pendingImagePreview}
                      resizeMode="cover"
                    />
                    {attachment.uploadStatus === 'uploading' && (
                      <View style={styles.uploadingOverlay}>
                        <ActivityIndicator size="small" color={Colors.light.surface} />
                      </View>
                    )}
                    <TouchableOpacity
                      onPress={() => {
                        const newAttachments = pendingAttachments.filter((_, i) => i !== index);
                        setPendingAttachments(newAttachments);
                      }}
                      style={styles.removePendingImageButton}
                    >
                      <X size={12} color={Colors.light.surface} />
                    </TouchableOpacity>
                  </View>
                ))}
              </ScrollView>
              <Text style={styles.pendingAttachmentsHint}>
                {pendingAttachments.length} de 3 imagen{pendingAttachments.length > 1 ? 'es' : ''}{' '}
                lista{pendingAttachments.length > 1 ? 's' : ''} • Escribe qué necesitas que analice
              </Text>
            </View>
          )}

          {/* Input */}
          <View style={styles.inputContainer}>
            <View style={styles.inputWrapper}>
              <TouchableOpacity
                style={styles.attachButton}
                onPress={() => handleImageUpload('library')}
                disabled={isSending || pendingAttachments.length >= 3}
              >
                <ImageIcon
                  size={20}
                  color={
                    isSending || pendingAttachments.length >= 3
                      ? Colors.light.textSecondary
                      : Colors.light.primary
                  }
                />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.attachButton}
                onPress={() => handleImageUpload('camera')}
                disabled={isSending || pendingAttachments.length >= 3}
              >
                <Camera
                  size={20}
                  color={
                    isSending || pendingAttachments.length >= 3
                      ? Colors.light.textSecondary
                      : Colors.light.primary
                  }
                />
              </TouchableOpacity>

              <TextInput
                style={styles.input}
                value={message}
                onChangeText={handleTextChange}
                placeholder={
                  pendingAttachments.length > 0
                    ? 'Describe qué necesitas que analice...'
                    : 'Pregúntame lo que necesites...'
                }
                placeholderTextColor={Colors.light.textSecondary + '60'}
                multiline
                editable={!isSending}
              />

              <TouchableOpacity
                style={[
                  styles.sendButton,
                  message.trim() && !isSending
                    ? styles.sendButtonActive
                    : styles.sendButtonInactive,
                ]}
                onPress={handleSend}
                disabled={!message.trim() || isSending}
                activeOpacity={0.8}
              >
                {isSending ? (
                  <ActivityIndicator size="small" color={Colors.light.surface} />
                ) : (
                  <Send
                    size={18}
                    color={message.trim() ? Colors.light.surface : Colors.light.textSecondary}
                  />
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>

      {/* Modals removed - using ActionSheet instead */}
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
    flexDirection: 'row',
  },

  // Sidebar
  sidebar: {
    width: 300,
    backgroundColor: Colors.light.surface,
    borderRightWidth: 1,
    borderRightColor: Colors.light.border,
  },
  sidebarOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    zIndex: 1000,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 2, height: 0 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  sidebarBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.light.backdropColor,
    zIndex: 999,
  },

  // Main content
  mainContent: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  mainContentWithSidebar: {
    marginLeft: 0, // Will be handled by flexDirection: 'row'
  },

  // Header - Minimal
  header: {
    backgroundColor: Colors.light.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border + '20',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  menuButton: {
    padding: spacing.xs,
  },
  homeButton: {
    padding: spacing.xs,
    borderRadius: radius.sm,
    backgroundColor: Colors.light.primary + '10',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
  },
  conversationIndicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: Colors.light.primary,
    marginLeft: spacing.xs,
  },

  // Welcome Screen - Chat-first with widgets
  welcomeContainer: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.xl,
  },
  welcomeContent: {
    alignItems: 'center',
    width: '100%',
    maxWidth: 600,
  },
  welcomeLogo: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.light.primary + '10',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.lg,
  },
  welcomeTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  welcomeSubtitle: {
    fontSize: typography.sizes.lg,
    color: Colors.light.textSecondary,
    marginBottom: spacing.lg,
  },

  // Widget containers
  widgetContainer: {
    width: '100%',
    marginBottom: spacing.lg,
  },

  // Suggestion Chips - Updated for chat-first
  suggestionChips: {
    width: '100%',
    marginTop: spacing.md,
  },
  startersTitle: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.textSecondary,
    marginBottom: spacing.sm,
    textAlign: 'left',
  },
  suggestionChip: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    borderWidth: 1,
    borderColor: Colors.light.border,
    marginRight: spacing.sm,
    marginBottom: spacing.sm,
  },
  suggestionText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.text,
  },

  // Messages
  messagesContainer: {
    flex: 1,
  },
  messagesContent: {
    padding: spacing.md,
    paddingBottom: spacing.xl,
    flexGrow: 1, // Asegura que el contenido crezca
  },
  messageContainer: {
    marginBottom: spacing.md,
    flexDirection: 'row',
    alignItems: 'flex-start',
    width: '100%', // Usar todo el ancho
    paddingHorizontal: spacing.xs, // Pequeño padding para evitar que toque los bordes
  },
  userMessageContainer: {
    justifyContent: 'flex-end',
  },
  assistantMessageContainer: {
    justifyContent: 'flex-start',
    flex: 1, // Permitir que el mensaje del asistente use todo el espacio disponible
  },

  // Assistant Avatar
  assistantAvatar: {
    width: 28, // Reducido para dar más espacio al texto
    height: 28,
    borderRadius: 14,
    backgroundColor: Colors.light.primary + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.xs, // Reducido margen
    marginTop: spacing.xs,
    flexShrink: 0, // Evita que se comprima
  },

  // Message Bubbles - Lighter style
  messageBubble: {
    maxWidth: '95%', // Aumentado de 85% para evitar cortes
    borderRadius: radius.md,
    padding: spacing.sm,
    flex: 1, // Permite que el contenido use todo el espacio disponible
  },
  userBubble: {
    backgroundColor: Colors.light.text + '08',
    marginLeft: spacing.xl,
  },
  assistantBubble: {
    backgroundColor: Colors.light.transparent,
    marginRight: 0, // Removido margen para usar más espacio
    flex: 1, // Usar todo el espacio disponible
    width: '100%', // Ancho completo
  },
  // messageText: {
  //   fontSize: typography.sizes.lg,
  //   lineHeight: typography.sizes.lg * 1.4,
  // },
  // userMessageText: {
  //   color: 'white',
  // },
  // assistantMessageText: {
  //   color: Colors.light.text,
  // },

  // Attachments
  attachmentsContainer: {
    marginBottom: spacing.sm,
  },
  attachmentContainer: {
    marginBottom: spacing.xs,
  },
  attachmentImage: {
    width: 200,
    height: 150,
    borderRadius: radius.md,
    backgroundColor: Colors.light.border,
  },

  // Bottom Container for suggestions and input - Fixed positioning
  bottomContainer: {
    backgroundColor: Colors.light.background,
    // Ensure the container is always at the bottom
    flexShrink: 0, // Prevent shrinking when keyboard appears
    minHeight: 'auto', // Allow natural height calculation
  },

  // Input - Minimal & Clean with better visibility
  inputContainer: {
    backgroundColor: Colors.light.background,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    // Add subtle shadow for better definition
    shadowColor: Colors.light.shadowColor,
    shadowOffset: { width: 0, height: -1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2, // Android shadow
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: Colors.light.surface,
    borderRadius: radius.xl,
    borderWidth: 1.5,
    borderColor: Colors.light.border,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    minHeight: 52, // Slightly taller for better touch targets
    maxHeight: 150, // Allow more expansion
    // Enhanced shadow for better depth
    shadowColor: Colors.light.shadowColor,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 3,
  },
  attachButton: {
    padding: spacing.sm,
    marginRight: spacing.xs,
    borderRadius: radius.sm,
    backgroundColor: 'transparent',
    // Better touch feedback
    opacity: 1,
  },
  input: {
    flex: 1,
    fontSize: typography.sizes.base, // Slightly smaller for better readability
    color: Colors.light.text,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.sm,
    maxHeight: 100, // Better max height
    lineHeight: typography.sizes.base * 1.5, // Better line spacing
    textAlignVertical: 'top', // Align text to top for multiline
  },
  sendButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: spacing.sm,
    // Enhanced shadow for the send button
    shadowColor: Colors.light.shadowColor,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 3,
    elevation: 4,
  },
  sendButtonActive: {
    backgroundColor: Colors.light.primary, // Use primary color instead of text
  },
  sendButtonInactive: {
    backgroundColor: Colors.light.textSecondary + '40',
  },

  // Pending attachments styles - ChatGPT/Claude style
  pendingAttachmentsContainer: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.sm,
    backgroundColor: Colors.light.background,
  },
  pendingAttachmentsScroll: {
    marginBottom: spacing.xs,
  },
  pendingAttachmentsContent: {
    paddingRight: spacing.sm,
  },
  pendingImageWrapper: {
    position: 'relative',
    marginRight: spacing.sm,
    borderRadius: radius.md,
    overflow: 'hidden',
    backgroundColor: Colors.light.surface,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  pendingImagePreview: {
    width: 80,
    height: 80,
    borderRadius: radius.md,
  },
  uploadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.light.text + '60',
    borderRadius: radius.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  removePendingImageButton: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: Colors.light.text + '80',
    borderRadius: 10,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  pendingAttachmentsHint: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

// Export memoized component for performance optimization
export default memo(ChatGPTInterface, (prevProps, nextProps) => {
  // Custom comparison for performance
  return (
    prevProps.conversationId === nextProps.conversationId &&
    prevProps.contextType === nextProps.contextType &&
    prevProps.contextId === nextProps.contextId &&
    prevProps.isModal === nextProps.isModal &&
    JSON.stringify(prevProps.contextData) === JSON.stringify(nextProps.contextData)
  );
});
