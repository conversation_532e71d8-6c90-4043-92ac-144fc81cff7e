import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { router } from 'expo-router';
import { 
  Zap, 
  Users, 
  Package, 
  BarChart3, 
  Calendar,
  AlertTriangle,
  TrendingUp,
  Clock
} from 'lucide-react-native';
import Colors from '@/constants/colors';
import { spacing, typography, radius } from '@/constants/theme';

interface QuickActionsProps {
  onActionPress?: (action: string) => void;
  style?: any;
}

interface QuickAction {
  id: string;
  title: string;
  subtitle: string;
  icon: React.ComponentType<any>;
  color: string;
  route: string;
  badge?: number;
}

export default function QuickActions({ onActionPress, style }: QuickActionsProps) {
  
  const quickActions: QuickAction[] = [
    {
      id: 'new-service',
      title: 'Nuevo Servicio',
      subtitle: 'Diagnóstico IA',
      icon: Zap,
      color: Colors.light.primary,
      route: '/service/client-selection',
    },
    {
      id: 'clients',
      title: 'Clientes',
      subtitle: 'Gestionar',
      icon: Users,
      color: '#10B981',
      route: '/clients',
    },
    {
      id: 'inventory',
      title: 'Inventario',
      subtitle: 'Stock actual',
      icon: Package,
      color: '#F59E0B',
      route: '/inventory',
      badge: 3, // Productos con stock bajo
    },
    {
      id: 'reports',
      title: 'Reportes',
      subtitle: 'Análisis',
      icon: BarChart3,
      color: '#8B5CF6',
      route: '/inventory/reports',
    },
  ];

  const handleActionPress = (action: QuickAction) => {
    onActionPress?.(action.id);
    router.push(action.route as any);
  };

  return (
    <View style={[styles.container, style]}>
      <Text style={styles.title}>Acciones Rápidas</Text>
      
      <View style={styles.actionsGrid}>
        {quickActions.map((action) => {
          const IconComponent = action.icon;
          
          return (
            <TouchableOpacity
              key={action.id}
              style={styles.actionCard}
              onPress={() => handleActionPress(action)}
              activeOpacity={0.7}
            >
              <View style={[styles.iconContainer, { backgroundColor: `${action.color}15` }]}>
                <IconComponent size={20} color={action.color} />
                {action.badge && (
                  <View style={styles.badge}>
                    <Text style={styles.badgeText}>{action.badge}</Text>
                  </View>
                )}
              </View>
              
              <View style={styles.actionContent}>
                <Text style={styles.actionTitle}>{action.title}</Text>
                <Text style={styles.actionSubtitle}>{action.subtitle}</Text>
              </View>
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: spacing.md,
  },
  title: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.textSecondary,
    marginBottom: spacing.sm,
    marginLeft: spacing.xs,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  actionCard: {
    flex: 1,
    minWidth: '47%',
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    padding: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: radius.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.sm,
    position: 'relative',
  },
  badge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: Colors.light.error,
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    fontSize: 10,
    fontWeight: typography.weights.bold,
    color: 'white',
  },
  actionContent: {
    flex: 1,
  },
  actionTitle: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
    marginBottom: 2,
  },
  actionSubtitle: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
  },
});
