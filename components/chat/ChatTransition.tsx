import React, { useEffect, useRef } from 'react';
import { Animated, StyleSheet } from 'react-native';

interface ChatTransitionProps {
  children: React.ReactNode;
  isVisible: boolean;
  direction?: 'slide' | 'fade';
  duration?: number;
}

export default function ChatTransition({ 
  children, 
  isVisible, 
  direction = 'slide',
  duration = 300 
}: ChatTransitionProps) {
  const animatedValue = useRef(new Animated.Value(isVisible ? 1 : 0)).current;

  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: isVisible ? 1 : 0,
      duration,
      useNativeDriver: true,
    }).start();
  }, [isVisible, duration]);

  const getAnimatedStyle = () => {
    if (direction === 'fade') {
      return {
        opacity: animatedValue,
      };
    }
    
    // Default slide animation
    return {
      opacity: animatedValue,
      transform: [
        {
          translateX: animatedValue.interpolate({
            inputRange: [0, 1],
            outputRange: [50, 0],
          }),
        },
      ],
    };
  };

  if (!isVisible && direction === 'slide') {
    return null;
  }

  return (
    <Animated.View style={[styles.container, getAnimatedStyle()]}>
      {children}
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
