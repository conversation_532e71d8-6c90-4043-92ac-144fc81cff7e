import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { router } from 'expo-router';
import { 
  TrendingUp, 
  AlertTriangle, 
  Clock, 
  Star,
  Calendar,
  Package
} from 'lucide-react-native';
import Colors from '@/constants/colors';
import { spacing, typography, radius } from '@/constants/theme';
import { useClientStore } from '@/stores/client-store';
import { useInventoryStore } from '@/stores/inventory-store';

interface SmartInsightsProps {
  style?: any;
}

interface Insight {
  id: string;
  type: 'warning' | 'info' | 'success' | 'urgent';
  title: string;
  description: string;
  action?: string;
  route?: string;
  icon: React.ComponentType<any>;
}

export default function SmartInsights({ style }: SmartInsightsProps) {
  const [insights, setInsights] = useState<Insight[]>([]);
  const { clients } = useClientStore();
  const { products } = useInventoryStore();

  useEffect(() => {
    generateInsights();
  }, [clients, products]);

  const generateInsights = () => {
    const newInsights: Insight[] = [];

    // Productos con stock bajo (solo si realmente hay)
    const lowStockProducts = products.filter(p => p.currentStock <= p.minStock);
    if (lowStockProducts.length > 0) {
      newInsights.push({
        id: 'low-stock',
        type: 'warning',
        title: `${lowStockProducts.length} productos con stock bajo`,
        description: 'Revisa tu inventario para evitar quedarte sin productos',
        action: 'Ver inventario',
        route: '/inventory',
        icon: Package,
      });
    }

    // Información útil sobre clientes (solo si hay clientes)
    if (clients.length > 0) {
      newInsights.push({
        id: 'client-info',
        type: 'info',
        title: `${clients.length} clientes registrados`,
        description: 'Gestiona la información de tus clientes',
        action: 'Ver clientes',
        route: '/clients',
        icon: Calendar,
      });
    }

    // Solo mostrar si hay insights reales
    setInsights(newInsights.slice(0, 2)); // Máximo 2 insights reales
  };

  const getInsightColor = (type: Insight['type']) => {
    switch (type) {
      case 'warning': return '#F59E0B';
      case 'urgent': return '#EF4444';
      case 'success': return '#10B981';
      case 'info': 
      default: return Colors.light.primary;
    }
  };

  const handleInsightPress = (insight: Insight) => {
    if (insight.route) {
      router.push(insight.route as any);
    }
  };

  if (insights.length === 0) {
    return null;
  }

  return (
    <View style={[styles.container, style]}>
      <Text style={styles.title}>Información Relevante</Text>
      
      {insights.map((insight) => {
        const IconComponent = insight.icon;
        const color = getInsightColor(insight.type);
        
        return (
          <TouchableOpacity
            key={insight.id}
            style={styles.insightCard}
            onPress={() => handleInsightPress(insight)}
            activeOpacity={0.7}
          >
            <View style={[styles.iconContainer, { backgroundColor: `${color}15` }]}>
              <IconComponent size={18} color={color} />
            </View>
            
            <View style={styles.insightContent}>
              <Text style={styles.insightTitle}>{insight.title}</Text>
              <Text style={styles.insightDescription}>{insight.description}</Text>
              {insight.action && (
                <Text style={[styles.insightAction, { color }]}>{insight.action}</Text>
              )}
            </View>
          </TouchableOpacity>
        );
      })}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: spacing.md,
  },
  title: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.textSecondary,
    marginBottom: spacing.sm,
    marginLeft: spacing.xs,
  },
  insightCard: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    padding: spacing.md,
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: radius.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.sm,
  },
  insightContent: {
    flex: 1,
  },
  insightTitle: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
    marginBottom: 2,
  },
  insightDescription: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
    lineHeight: 16,
    marginBottom: 4,
  },
  insightAction: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium,
  },
});
