import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { 
  TrendingUp, 
  Users, 
  Package, 
  DollarSign,
  Calendar
} from 'lucide-react-native';
import Colors from '@/constants/colors';
import { spacing, typography, radius } from '@/constants/theme';
import { useDashboardStore } from '@/stores/dashboard-store';
import { useInventoryStore } from '@/stores/inventory-store';

interface QuickMetricsProps {
  style?: any;
}

interface MetricItem {
  id: string;
  label: string;
  value: string;
  change?: string;
  changeType?: 'positive' | 'negative' | 'neutral';
  icon: React.ComponentType<any>;
  color: string;
}

export default function QuickMetrics({ style }: QuickMetricsProps) {
  const { metrics, loadTodayMetrics } = useDashboardStore();
  const { products } = useInventoryStore();
  const [quickMetrics, setQuickMetrics] = useState<MetricItem[]>([]);

  useEffect(() => {
    loadTodayMetrics();
  }, []);

  useEffect(() => {
    if (metrics) {
      const newMetrics: MetricItem[] = [
        {
          id: 'services-today',
          label: 'Servicios hoy',
          value: metrics.servicesCount?.toString() || '0',
          icon: Calendar,
          color: Colors.light.primary,
        },
        {
          id: 'clients-total',
          label: 'Total clientes',
          value: metrics.activeClients?.toString() || '0',
          icon: Users,
          color: '#8B5CF6',
        },
      ];

      // Only add low stock if there are actually products with low stock
      const lowStockCount = products.filter(p => p.currentStock <= p.minStock).length;
      if (lowStockCount > 0) {
        newMetrics.push({
          id: 'products-low',
          label: 'Stock bajo',
          value: lowStockCount.toString(),
          changeType: 'negative',
          icon: Package,
          color: '#F59E0B',
        });
      }

      setQuickMetrics(newMetrics);
    }
  }, [metrics, products]);

  const getChangeColor = (changeType?: string) => {
    switch (changeType) {
      case 'positive': return '#10B981';
      case 'negative': return '#EF4444';
      default: return Colors.light.textSecondary;
    }
  };

  if (quickMetrics.length === 0) {
    return null;
  }

  return (
    <View style={[styles.container, style]}>
      <Text style={styles.title}>Resumen de Hoy</Text>
      
      <View style={styles.metricsGrid}>
        {quickMetrics.map((metric) => {
          const IconComponent = metric.icon;
          
          return (
            <View key={metric.id} style={styles.metricCard}>
              <View style={styles.metricHeader}>
                <View style={[styles.iconContainer, { backgroundColor: `${metric.color}15` }]}>
                  <IconComponent size={16} color={metric.color} />
                </View>
                {metric.change && (
                  <Text style={[styles.changeText, { color: getChangeColor(metric.changeType) }]}>
                    {metric.change}
                  </Text>
                )}
              </View>
              
              <Text style={styles.metricValue}>{metric.value}</Text>
              <Text style={styles.metricLabel}>{metric.label}</Text>
            </View>
          );
        })}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: spacing.md,
  },
  title: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.textSecondary,
    marginBottom: spacing.sm,
    marginLeft: spacing.xs,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  metricCard: {
    flex: 1,
    minWidth: '47%',
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    padding: spacing.md,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  metricHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  iconContainer: {
    width: 28,
    height: 28,
    borderRadius: radius.sm,
    justifyContent: 'center',
    alignItems: 'center',
  },
  changeText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium,
  },
  metricValue: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    marginBottom: 2,
  },
  metricLabel: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
  },
});
