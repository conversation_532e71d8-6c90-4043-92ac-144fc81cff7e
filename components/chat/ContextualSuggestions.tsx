import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { 
  Lightbulb, 
  Camera, 
  Palette, 
  Calculator,
  BookOpen,
  Zap
} from 'lucide-react-native';
import Colors from '@/constants/colors';
import { spacing, typography, radius } from '@/constants/theme';
import { usePathname } from 'expo-router';

interface ContextualSuggestionsProps {
  onSuggestionPress: (suggestion: string) => void;
  style?: any;
}

interface Suggestion {
  id: string;
  text: string;
  icon: React.ComponentType<any>;
  color: string;
  context?: string[];
}

export default function ContextualSuggestions({ 
  onSuggestionPress, 
  style 
}: ContextualSuggestionsProps) {
  const pathname = usePathname();
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);

  const allSuggestions: Suggestion[] = [
    {
      id: 'analyze-photo',
      text: '📸 Analizar foto de cabello',
      icon: Camera,
      color: Colors.light.primary,
      context: ['general', 'service'],
    },
    {
      id: 'color-formula',
      text: '🧪 Crear fórmula de color',
      icon: Palette,
      color: '#8B5CF6',
      context: ['general', 'service', 'client'],
    },
    {
      id: 'calculate-mixture',
      text: '⚖️ Calcular mezcla de productos',
      icon: Calculator,
      color: '#10B981',
      context: ['general', 'inventory'],
    },
    {
      id: 'color-theory',
      text: '📚 Explicar teoría del color',
      icon: BookOpen,
      color: '#F59E0B',
      context: ['general'],
    },
    {
      id: 'quick-diagnosis',
      text: '⚡ Diagnóstico rápido',
      icon: Zap,
      color: '#EF4444',
      context: ['general', 'client'],
    },
    {
      id: 'product-recommendation',
      text: '💡 Recomendar productos',
      icon: Lightbulb,
      color: '#06B6D4',
      context: ['general', 'inventory'],
    },
  ];

  useEffect(() => {
    // Filter suggestions based on current context
    const currentContext = getCurrentContext();
    const filteredSuggestions = allSuggestions.filter(suggestion => 
      suggestion.context?.includes(currentContext) || suggestion.context?.includes('general')
    );
    
    // Randomize and limit to 4 suggestions
    const shuffled = filteredSuggestions.sort(() => 0.5 - Math.random());
    setSuggestions(shuffled.slice(0, 4));
  }, [pathname]);

  const getCurrentContext = (): string => {
    if (pathname.includes('/service/')) return 'service';
    if (pathname.includes('/client/')) return 'client';
    if (pathname.includes('/inventory/')) return 'inventory';
    return 'general';
  };

  const handleSuggestionPress = (suggestion: Suggestion) => {
    onSuggestionPress(suggestion.text);
  };

  if (suggestions.length === 0) {
    return null;
  }

  return (
    <View style={[styles.container, style]}>
      <Text style={styles.title}>Sugerencias</Text>
      
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {suggestions.map((suggestion) => {
          const IconComponent = suggestion.icon;
          
          return (
            <TouchableOpacity
              key={suggestion.id}
              style={[styles.suggestionCard, { borderColor: `${suggestion.color}30` }]}
              onPress={() => handleSuggestionPress(suggestion)}
              activeOpacity={0.7}
            >
              <View style={[styles.iconContainer, { backgroundColor: `${suggestion.color}15` }]}>
                <IconComponent size={18} color={suggestion.color} />
              </View>
              <Text style={styles.suggestionText}>{suggestion.text}</Text>
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: spacing.md,
  },
  title: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.textSecondary,
    marginBottom: spacing.sm,
    marginLeft: spacing.xs,
  },
  scrollContent: {
    paddingHorizontal: spacing.xs,
  },
  suggestionCard: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    padding: spacing.md,
    marginRight: spacing.sm,
    minWidth: 140,
    maxWidth: 160,
    borderWidth: 1,
    borderColor: Colors.light.border,
    alignItems: 'center',
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: radius.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  suggestionText: {
    fontSize: typography.sizes.xs,
    color: Colors.light.text,
    textAlign: 'center',
    lineHeight: 16,
  },
});
